// UI增强脚本 - 提供更流畅的动画和交互效果

class UIEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.addRippleEffect();
        this.addSmoothTransitions();
        this.addMagneticEffects();
        this.addParallaxEffects();
        this.addLoadingSequence();
    }

    // 添加波纹点击效果
    addRippleEffect() {
        const buttons = document.querySelectorAll('.menu-btn, .mode-btn, .control-button, .back-btn');
        
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.6);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                    z-index: 10;
                `;
                
                button.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // 添加波纹动画CSS
        if (!document.getElementById('ripple-styles')) {
            const style = document.createElement('style');
            style.id = 'ripple-styles';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // 添加流畅的页面切换
    addSmoothTransitions() {
        const originalShowScreen = window.gameManager?.showScreen;
        if (originalShowScreen) {
            window.gameManager.showScreen = (screenId) => {
                const currentScreen = document.querySelector('.menu-screen:not(.hidden)');
                const targetScreen = document.getElementById(screenId + 'Screen') || document.getElementById(screenId);
                
                if (currentScreen && targetScreen && currentScreen !== targetScreen) {
                    // 添加退出动画
                    currentScreen.style.transition = 'all 0.6s cubic-bezier(0.23, 1, 0.32, 1)';
                    currentScreen.style.transform = 'translateX(-100%) scale(0.9)';
                    currentScreen.style.opacity = '0';
                    currentScreen.style.filter = 'blur(5px)';
                    
                    setTimeout(() => {
                        currentScreen.classList.add('hidden');
                        currentScreen.style.transform = '';
                        currentScreen.style.opacity = '';
                        currentScreen.style.filter = '';
                        
                        // 添加进入动画
                        targetScreen.classList.remove('hidden');
                        targetScreen.style.transform = 'translateX(100%) scale(0.9)';
                        targetScreen.style.opacity = '0';
                        targetScreen.style.filter = 'blur(5px)';
                        
                        requestAnimationFrame(() => {
                            targetScreen.style.transition = 'all 0.8s cubic-bezier(0.23, 1, 0.32, 1)';
                            targetScreen.style.transform = 'translateX(0) scale(1)';
                            targetScreen.style.opacity = '1';
                            targetScreen.style.filter = 'blur(0px)';
                        });
                    }, 300);
                } else {
                    originalShowScreen.call(window.gameManager, screenId);
                }
            };
        }
    }

    // 添加磁性悬停效果
    addMagneticEffects() {
        const magneticElements = document.querySelectorAll('.info-item, .menu-btn, .mode-btn');
        
        magneticElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                this.style.transition = 'all 0.6s cubic-bezier(0.23, 1, 0.32, 1)';
                this.style.transform = 'translateY(-8px) scale(1.02) rotateX(5deg)';
                this.style.filter = 'brightness(1.1)';
            });
            
            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1) rotateX(0deg)';
                this.style.filter = 'brightness(1)';
            });
            
            element.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                const rotateX = (y / rect.height) * 10;
                const rotateY = (x / rect.width) * -10;
                
                this.style.transform = `translateY(-8px) scale(1.02) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });
        });
    }

    // 添加视差效果
    addParallaxEffects() {
        let ticking = false;
        
        function updateParallax() {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.menu-container, .game-container');
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.05 * (index + 1);
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
            
            ticking = false;
        }
        
        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestTick);
    }

    // 添加加载序列动画
    addLoadingSequence() {
        document.addEventListener('DOMContentLoaded', () => {
            // 为菜单按钮添加序列动画
            const menuButtons = document.querySelectorAll('.menu-btn');
            menuButtons.forEach((button, index) => {
                button.style.opacity = '0';
                button.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    button.style.transition = 'all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                    button.style.opacity = '1';
                    button.style.transform = 'translateY(0)';
                }, 100 + index * 100);
            });
            
            // 为信息项添加序列动画
            const infoItems = document.querySelectorAll('.info-item');
            infoItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(-30px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, 200 + index * 150);
            });
        });
    }

    // 添加数值动画
    animateValue(element, start, end, duration = 1000) {
        let startTimestamp = null;
        
        const step = (timestamp) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            const value = Math.floor(progress * (end - start) + start);
            element.textContent = value;
            
            if (progress < 1) {
                window.requestAnimationFrame(step);
            }
        };
        
        window.requestAnimationFrame(step);
    }

    // 添加通知动画
    showNotification(message, type = 'success', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification-enhanced ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 进入动画
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        });
        
        // 退出动画
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, duration);
    }

    // 添加粒子效果
    createParticles(x, y, count = 10) {
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle-enhanced';
            particle.style.left = x + 'px';
            particle.style.top = y + 'px';
            
            const angle = (Math.PI * 2 * i) / count;
            const velocity = 50 + Math.random() * 50;
            const deltaX = Math.cos(angle) * velocity;
            const deltaY = Math.sin(angle) * velocity;
            
            particle.style.setProperty('--deltaX', deltaX + 'px');
            particle.style.setProperty('--deltaY', deltaY + 'px');
            
            document.body.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 3000);
        }
    }
}

// 初始化UI增强器
window.uiEnhancer = new UIEnhancer();

// 导出给其他脚本使用
window.UIEnhancer = UIEnhancer;
