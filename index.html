<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级贪吃蛇大冒险</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐍</text></svg>">
</head>
<body>
    <!-- 主菜单 -->
    <div id="mainMenu" class="menu-screen">
        <div class="menu-container">
            <h1 class="game-title" data-text="🐍 超级贪吃蛇大冒险">🐍 超级贪吃蛇大冒险</h1>
            <div class="menu-buttons">
                <button class="menu-btn" onclick="showGameMode()">🎮 开始游戏</button>
                <button class="menu-btn" onclick="showSkinShop()">🎨 皮肤商店</button>
                <button class="menu-btn" onclick="showLevelEditor()">🛠️ 关卡编辑器</button>
                <button class="menu-btn" onclick="showAchievements()">🏅 成就系统</button>
                <button class="menu-btn" onclick="showLeaderboard()">🏆 排行榜</button>
                <button class="menu-btn" onclick="showSettings()">⚙️ 设置</button>
                <button class="menu-btn" onclick="showInstructions()">📖 游戏说明</button>
                <button class="menu-btn" onclick="startTutorial()">🎓 新手教程</button>
            </div>
        </div>
    </div>

    <!-- 游戏模式选择 -->
    <div id="gameModeMenu" class="menu-screen hidden">
        <div class="menu-container">
            <h2>选择游戏模式</h2>
            <div class="mode-buttons">
                <button class="mode-btn" onclick="startClassicMode()">
                    <h3>🐍 经典模式</h3>
                    <p>传统的贪吃蛇游戏</p>
                </button>
                <button class="mode-btn" onclick="startAdventureMode()">
                    <h3>🗺️ 冒险模式</h3>
                    <p>闯关挑战，解锁新关卡</p>
                </button>
                <button class="mode-btn" onclick="startTimeAttackMode()">
                    <h3>⏰ 限时模式</h3>
                    <p>在限定时间内获得最高分</p>
                </button>
                <button class="mode-btn" onclick="startMultiplayerMode()">
                    <h3>👥 多人对战</h3>
                    <p>与朋友一起竞技</p>
                </button>
                <button class="mode-btn" onclick="startDailyChallengeMode()">
                    <h3>📅 每日挑战</h3>
                    <p>每天一个特殊挑战</p>
                </button>
                <button class="mode-btn" onclick="startSurvivalMode()">
                    <h3>💀 生存模式</h3>
                    <p>在危险环境中生存</p>
                </button>
                <button class="mode-btn" onclick="startBossMode()">
                    <h3>👹 Boss战模式</h3>
                    <p>挑战强大的Boss</p>
                </button>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 游戏界面 -->
    <div id="gameScreen" class="game-screen hidden">
        <div class="game-container">
            <div class="game-header">
                <div class="game-info">
                    <div class="info-item">
                        <span class="label">得分:</span>
                        <span id="score" class="value">0</span>
                    </div>
                    <div class="info-item">
                        <span class="label">关卡:</span>
                        <span id="level" class="value">1</span>
                    </div>
                    <div class="info-item">
                        <span class="label">长度:</span>
                        <span id="length" class="value">1</span>
                    </div>
                    <div class="info-item" id="timeInfo">
                        <span class="label">时间:</span>
                        <span id="time" class="value">00:00</span>
                    </div>
                    <div class="info-item" id="comboInfo">
                        <span class="label">连击:</span>
                        <span id="combo" class="value">0</span>
                    </div>
                </div>
                <div class="boss-health-container" id="bossHealthContainer" style="display: none;">
                    <div class="boss-info">
                        <span id="bossName">Boss名称</span>
                        <span id="bossLevel">Lv.1</span>
                    </div>
                    <div class="boss-health-bar">
                        <div class="boss-health-fill" id="bossHealthFill"></div>
                        <span class="boss-health-text" id="bossHealthText">100/100</span>
                    </div>
                </div>
                <div class="game-status" id="game-status">准备开始...</div>
            </div>

            <div class="game-area">
                <canvas id="gameCanvas" width="600" height="600"></canvas>
                <div class="effects-layer" id="effectsLayer"></div>
            </div>

            <div class="game-controls">
                <button id="startBtn" class="control-button">开始游戏</button>
                <button id="pauseBtn" class="control-button">暂停</button>
                <button id="resetBtn" class="control-button">重新开始</button>
                <button id="menuBtn" class="control-button" onclick="showMainMenu()">主菜单</button>
            </div>

            <div class="keyboard-hints">
                <div class="hint-item">
                    <span class="key">方向键</span>
                    <span class="action">移动</span>
                </div>
                <div class="hint-item">
                    <span class="key">空格</span>
                    <span class="action">暂停/继续</span>
                </div>
                <div class="hint-item">
                    <span class="key">ESC</span>
                    <span class="action">返回菜单</span>
                </div>
            </div>

            <div class="mobile-controls">
                <div class="control-row">
                    <button class="control-btn" data-direction="up">↑</button>
                </div>
                <div class="control-row">
                    <button class="control-btn" data-direction="left">←</button>
                    <button class="control-btn" data-direction="down">↓</button>
                    <button class="control-btn" data-direction="right">→</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 排行榜 -->
    <div id="leaderboardScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>🏆 排行榜</h2>
            <div class="leaderboard-tabs">
                <button class="tab-btn active" onclick="showLeaderboardTab('classic')">经典模式</button>
                <button class="tab-btn" onclick="showLeaderboardTab('adventure')">冒险模式</button>
                <button class="tab-btn" onclick="showLeaderboardTab('timeattack')">限时模式</button>
            </div>
            <div id="leaderboardContent" class="leaderboard-content">
                <!-- 排行榜内容将在这里动态生成 -->
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 设置界面 -->
    <div id="settingsScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>⚙️ 设置</h2>
            <div class="settings-content">
                <div class="setting-item">
                    <label>音效:</label>
                    <input type="checkbox" id="soundToggle" checked>
                </div>
                <div class="setting-item">
                    <label>游戏速度:</label>
                    <select id="speedSelect">
                        <option value="slow">慢速</option>
                        <option value="normal" selected>正常</option>
                        <option value="fast">快速</option>
                        <option value="extreme">极速</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>主题:</label>
                    <select id="themeSelect">
                        <option value="classic">经典</option>
                        <option value="neon">霓虹</option>
                        <option value="nature">自然</option>
                        <option value="cyberpunk">赛博朋克</option>
                        <option value="retro">复古</option>
                        <option value="galaxy">银河</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>粒子效果:</label>
                    <input type="checkbox" id="particleToggle" checked>
                </div>
                <div class="setting-item">
                    <label>屏幕震动:</label>
                    <input type="checkbox" id="vibrationToggle" checked>
                </div>
                <div class="setting-item">
                    <label>显示轨迹:</label>
                    <input type="checkbox" id="trailToggle">
                </div>
                <div class="setting-item">
                    <label>背景音乐:</label>
                    <input type="checkbox" id="musicToggle" checked>
                </div>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 游戏说明 -->
    <div id="instructionsScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>📖 游戏说明</h2>
            <div class="instructions-content">
                <div class="instruction-section">
                    <h3>基本操作</h3>
                    <p>• 使用方向键或屏幕按钮控制蛇的移动</p>
                    <p>• 吃到食物可以增长身体和得分</p>
                    <p>• 避免撞到墙壁或自己的身体</p>
                </div>
                <div class="instruction-section">
                    <h3>特殊道具</h3>
                    <p>🍎 普通食物 - 增加10分</p>
                    <p>🍇 特殊食物 - 增加50分</p>
                    <p>⚡ 加速道具 - 临时提升速度</p>
                    <p>🛡️ 护盾道具 - 免疫一次碰撞</p>
                </div>
                <div class="instruction-section">
                    <h3>游戏模式</h3>
                    <p>• 经典模式：传统玩法，挑战最高分</p>
                    <p>• 冒险模式：闯关挑战，解锁新关卡</p>
                    <p>• 限时模式：在限定时间内获得最高分</p>
                </div>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 皮肤商店 -->
    <div id="skinShopScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>🎨 皮肤商店</h2>
            <div class="shop-content">
                <div class="currency-display">
                    <span class="currency-label">🪙 金币:</span>
                    <span id="playerCoins" class="currency-value">0</span>
                </div>
                <div class="skin-grid" id="skinGrid">
                    <!-- 皮肤项目将在这里动态生成 -->
                </div>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 关卡编辑器 -->
    <div id="levelEditorScreen" class="menu-screen hidden">
        <div class="menu-container level-editor-container">
            <h2>🛠️ 关卡编辑器</h2>
            <div class="editor-controls">
                <div class="tool-palette">
                    <button class="tool-btn active" data-tool="wall">🧱 墙壁</button>
                    <button class="tool-btn" data-tool="food">🍎 食物</button>
                    <button class="tool-btn" data-tool="powerup">⚡ 道具</button>
                    <button class="tool-btn" data-tool="spawn">🐍 起点</button>
                    <button class="tool-btn" data-tool="erase">🗑️ 擦除</button>
                </div>
                <div class="editor-actions">
                    <button class="action-btn" onclick="clearLevel()">清空</button>
                    <button class="action-btn" onclick="saveLevel()">保存</button>
                    <button class="action-btn" onclick="loadLevel()">加载</button>
                    <button class="action-btn" onclick="testLevel()">测试</button>
                </div>
            </div>
            <div class="editor-canvas-container">
                <canvas id="editorCanvas" width="600" height="600"></canvas>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- 多人对战界面 -->
    <div id="multiplayerScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>👥 多人对战</h2>
            <div class="multiplayer-options">
                <button class="mode-btn" onclick="startLocalMultiplayer()">
                    <h3>🏠 本地对战</h3>
                    <p>同一设备上的双人游戏</p>
                </button>
                <button class="mode-btn" onclick="createRoom()">
                    <h3>🌐 创建房间</h3>
                    <p>创建在线对战房间</p>
                </button>
                <button class="mode-btn" onclick="joinRoom()">
                    <h3>🔗 加入房间</h3>
                    <p>输入房间代码加入游戏</p>
                </button>
            </div>
            <button class="back-btn" onclick="showGameMode()">返回</button>
        </div>
    </div>

    <!-- 每日挑战界面 -->
    <div id="dailyChallengeScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>📅 每日挑战</h2>
            <div class="challenge-info">
                <div class="challenge-card" id="todayChallenge">
                    <!-- 今日挑战内容将在这里动态生成 -->
                </div>
                <div class="challenge-rewards">
                    <h3>🎁 奖励</h3>
                    <div class="reward-list" id="challengeRewards">
                        <!-- 奖励列表将在这里动态生成 -->
                    </div>
                </div>
            </div>
            <button class="challenge-start-btn" onclick="startDailyChallenge()">开始挑战</button>
            <button class="back-btn" onclick="showGameMode()">返回</button>
        </div>
    </div>

    <!-- 成就系统界面 -->
    <div id="achievementsScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>🏅 成就系统</h2>
            <div class="achievements-content">
                <div class="achievement-stats">
                    <div class="stat-item">
                        <span class="stat-label">总游戏时间:</span>
                        <span id="totalPlayTime" class="stat-value">0分钟</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">获得成就:</span>
                        <span id="achievementCount" class="stat-value">0/20</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">完成度:</span>
                        <span id="completionRate" class="stat-value">0%</span>
                    </div>
                </div>
                <div class="achievements-grid" id="achievementsGrid">
                    <!-- 成就列表将在这里动态生成 -->
                </div>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回</button>
        </div>
    </div>

    <!-- Boss战界面 -->
    <div id="bossScreen" class="menu-screen hidden">
        <div class="menu-container">
            <h2>👹 Boss战模式</h2>
            <div class="boss-selection">
                <h3>选择挑战的Boss</h3>
                <div class="boss-grid" id="bossGrid">
                    <!-- Boss列表将在这里动态生成 -->
                </div>
            </div>
            <button class="back-btn" onclick="showGameMode()">返回</button>
        </div>
    </div>

    <!-- 新手教程界面 -->
    <div id="tutorialScreen" class="menu-screen hidden">
        <div class="menu-container tutorial-container">
            <h2>🎓 新手教程</h2>
            <div class="tutorial-content" id="tutorialContent">
                <!-- 教程内容将在这里动态生成 -->
            </div>
            <div class="tutorial-controls">
                <button id="prevTutorialBtn" class="tutorial-btn" onclick="gameManager.prevTutorialStep()">上一步</button>
                <span id="tutorialProgress" class="tutorial-progress">1/5</span>
                <button id="nextTutorialBtn" class="tutorial-btn" onclick="gameManager.nextTutorialStep()">下一步</button>
            </div>
            <div class="tutorial-actions">
                <button class="tutorial-btn skip-btn" onclick="gameManager.skipTutorial()">跳过教程</button>
                <button class="tutorial-btn start-btn" onclick="gameManager.startTutorialGame()" style="display: none;">开始练习</button>
            </div>
            <button class="back-btn" onclick="showMainMenu()">返回主菜单</button>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="snake-loader">
                <div class="snake-segment"></div>
                <div class="snake-segment"></div>
                <div class="snake-segment"></div>
                <div class="snake-segment"></div>
            </div>
            <h2>🐍 超级贪吃蛇大冒险</h2>
            <p>正在加载中...</p>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="ui-enhancements.js"></script>
</body>
</html>
