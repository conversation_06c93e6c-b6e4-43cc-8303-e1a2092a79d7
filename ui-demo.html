<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI改进演示 - 超级贪吃蛇大冒险</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;600;700;900&display=swap" rel="stylesheet">
    <style>
        .demo-container {
            padding: 50px 20px;
            max-width: 1200px;
            margin: 0 auto;
            opacity: 0;
            animation: fadeInContainer 1s ease-out 0.5s forwards;
        }

        @keyframes fadeInContainer {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .demo-section {
            margin-bottom: 80px;
            text-align: center;
            opacity: 0;
            animation: slideInUp 0.8s cubic-bezier(0.23, 1, 0.32, 1) forwards;
        }

        .demo-section:nth-child(1) { animation-delay: 0.2s; }
        .demo-section:nth-child(2) { animation-delay: 0.4s; }
        .demo-section:nth-child(3) { animation-delay: 0.6s; }
        .demo-section:nth-child(4) { animation-delay: 0.8s; }
        .demo-section:nth-child(5) { animation-delay: 1s; }
        
        .demo-title {
            font-size: 2.5em;
            margin-bottom: 30px;
            color: var(--primary-color);
            font-weight: 700;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .demo-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
        }
        
        .demo-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow:
                var(--shadow-heavy),
                0 0 40px rgba(78, 205, 196, 0.2);
            border-color: rgba(78, 205, 196, 0.3);
            transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
        }
        
        .button-showcase {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }
        
        .info-showcase {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }
        
        .game-over-demo {
            position: relative;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .demo-game-over {
            position: relative;
            background: 
                linear-gradient(135deg, rgba(15, 15, 35, 0.98) 0%, rgba(26, 26, 46, 0.98) 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 30px;
            text-align: center;
            box-shadow: 
                0 30px 80px rgba(0, 0, 0, 0.8),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px) saturate(180%);
            max-width: 400px;
            width: 100%;
            overflow: hidden;
        }
        
        .demo-game-over::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e74c3c, #f39c12, #e74c3c);
            background-size: 200% 100%;
            animation: gradientMove 3s ease-in-out infinite;
        }
        
        .demo-game-over h2 {
            background: linear-gradient(45deg, #e74c3c, #f39c12, #e74c3c);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            font-size: 2.2em;
            font-weight: 900;
            animation: gradientShift 3s ease-in-out infinite;
            letter-spacing: 1px;
        }
        
        .demo-stats {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .demo-stat-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        }
        
        .demo-stat-row:last-child {
            border-bottom: none;
        }
        
        .demo-stat-label {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .demo-stat-value {
            color: #4ecdc4;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(78, 205, 196, 0.4);
        }
        
        .demo-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(39, 174, 96, 0.5);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 标题演示 -->
        <div class="demo-section">
            <h1 class="game-title" data-text="🐍 超级贪吃蛇大冒险">🐍 超级贪吃蛇大冒险</h1>
            <p style="font-size: 1.2em; opacity: 0.8; margin-top: 20px;">现代化UI设计演示</p>
        </div>

        <!-- 按钮演示 -->
        <div class="demo-section">
            <h2 class="demo-title">按钮样式</h2>
            <div class="demo-grid">
                <div class="demo-card hover-lift">
                    <h3 style="margin-bottom: 20px; color: white;">主要按钮</h3>
                    <div class="button-showcase stagger-animation">
                        <button class="menu-btn pulse-effect">🎮 开始游戏</button>
                        <button class="menu-btn magnetic-hover">🎨 皮肤商店</button>
                        <button class="menu-btn glow-effect">🏅 成就系统</button>
                    </div>
                </div>
                
                <div class="demo-card">
                    <h3 style="margin-bottom: 20px; color: white;">模式按钮</h3>
                    <div class="button-showcase">
                        <button class="mode-btn">
                            <h4 style="margin: 0 0 5px 0;">🐍 经典模式</h4>
                            <p style="margin: 0; font-size: 14px; opacity: 0.8;">传统的贪吃蛇游戏</p>
                        </button>
                        <button class="mode-btn">
                            <h4 style="margin: 0 0 5px 0;">⏰ 限时模式</h4>
                            <p style="margin: 0; font-size: 14px; opacity: 0.8;">在限定时间内获得最高分</p>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 信息项演示 -->
        <div class="demo-section">
            <h2 class="demo-title">信息显示</h2>
            <div class="demo-card">
                <h3 style="margin-bottom: 20px; color: white;">游戏信息</h3>
                <div class="info-showcase">
                    <div class="info-item">
                        <span class="label">得分:</span>
                        <span class="value">1250</span>
                    </div>
                    <div class="info-item">
                        <span class="label">关卡:</span>
                        <span class="value">5</span>
                    </div>
                    <div class="info-item">
                        <span class="label">长度:</span>
                        <span class="value">15</span>
                    </div>
                    <div class="info-item">
                        <span class="label">时间:</span>
                        <span class="value">02:30</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 游戏结束界面演示 -->
        <div class="demo-section">
            <h2 class="demo-title">游戏结束界面</h2>
            <div class="demo-card">
                <div class="game-over-demo">
                    <div class="demo-game-over">
                        <h2>游戏结束!</h2>
                        <p style="margin: 10px 0;">游戏模式: <strong>限时模式</strong></p>
                        <div class="final-score" style="font-size: 1.8em; color: #4ecdc4; font-weight: bold; margin: 15px 0;">1250</div>
                        
                        <div class="demo-stats">
                            <div class="demo-stat-row">
                                <span class="demo-stat-label">游戏时长:</span>
                                <span class="demo-stat-value">2:30</span>
                            </div>
                            <div class="demo-stat-row">
                                <span class="demo-stat-label">蛇身长度:</span>
                                <span class="demo-stat-value">15</span>
                            </div>
                            <div class="demo-stat-row">
                                <span class="demo-stat-label">食物数量:</span>
                                <span class="demo-stat-value">14</span>
                            </div>
                            <div class="demo-stat-row">
                                <span class="demo-stat-label">平均每食物得分:</span>
                                <span class="demo-stat-value">89</span>
                            </div>
                        </div>
                        
                        <div class="demo-buttons">
                            <button class="demo-btn">重新开始</button>
                            <button class="demo-btn">查看排行榜</button>
                            <button class="demo-btn">返回主菜单</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮演示 -->
        <div class="demo-section">
            <h2 class="demo-title">其他按钮</h2>
            <div class="demo-card">
                <h3 style="margin-bottom: 20px; color: white;">返回按钮</h3>
                <button class="back-btn">返回主菜单</button>
            </div>
        </div>
    </div>

    <script>
        // 添加交互式动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为按钮添加点击波纹效果
            function createRipple(event) {
                const button = event.currentTarget;
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = event.clientX - rect.left - size / 2;
                const y = event.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                button.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }

            // 为所有按钮添加波纹效果
            const buttons = document.querySelectorAll('.menu-btn, .mode-btn, .demo-btn, .back-btn');
            buttons.forEach(button => {
                button.addEventListener('click', createRipple);
            });

            // 添加鼠标跟随效果
            let mouseX = 0, mouseY = 0;
            let cursorX = 0, cursorY = 0;

            document.addEventListener('mousemove', (e) => {
                mouseX = e.clientX;
                mouseY = e.clientY;
            });

            // 为信息项添加磁性效果
            const infoItems = document.querySelectorAll('.info-item');
            infoItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.05) rotateX(5deg)';
                    this.style.transition = 'all 0.6s cubic-bezier(0.23, 1, 0.32, 1)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1) rotateX(0deg)';
                });
            });

            // 添加滚动视差效果
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelectorAll('.demo-card');
                const speed = 0.1;

                parallax.forEach((element, index) => {
                    const yPos = -(scrolled * speed * (index + 1));
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });

            // 添加数值动画效果
            function animateValue(element, start, end, duration) {
                let startTimestamp = null;
                const step = (timestamp) => {
                    if (!startTimestamp) startTimestamp = timestamp;
                    const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                    const value = Math.floor(progress * (end - start) + start);
                    element.textContent = value;
                    if (progress < 1) {
                        window.requestAnimationFrame(step);
                    }
                };
                window.requestAnimationFrame(step);
            }

            // 为演示统计数据添加动画
            const statValues = document.querySelectorAll('.demo-stat-value');
            statValues.forEach((value, index) => {
                const finalValue = parseInt(value.textContent);
                if (!isNaN(finalValue)) {
                    value.textContent = '0';
                    setTimeout(() => {
                        animateValue(value, 0, finalValue, 1500);
                    }, 2000 + index * 200);
                }
            });
        });

        // 添加CSS样式用于波纹效果
        const style = document.createElement('style');
        style.textContent = `
            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            .demo-card {
                transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
